[build]
  # Build command
  command = "npm run build"

  # Directory to publish (Vite builds to 'dist')
  publish = "dist"

[build.environment]
  # Node.js version and environment variables for build process
  NODE_VERSION = "18"
  VITE_SUPABASE_URL = "https://olnhzuxguxjwcbsxytju.supabase.co"
  VITE_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sbmh6dXhndXhqd2Nic3h5dGp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTc3MDEsImV4cCI6MjA2NDE5MzcwMX0.zKcLCo1k2G4pwOyV2lnfKTkPeKdMQV1W1SKd81hWTxQ"
  # Stripe environment variables (you'll need to set these in Netlify dashboard)
  VITE_STRIPE_PUBLISHABLE_KEY = "pk_test_your_stripe_publishable_key_here"

# SPA (Single Page Application) redirects for React Router
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/*.ico"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
