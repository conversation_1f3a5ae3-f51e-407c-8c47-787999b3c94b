# 📋 Manual Edge Function Deployment

## 🎯 Quick Fix: Deploy via Supabase Dashboard

Since Docker is not available, here's how to manually deploy the functions:

### Step 1: Create `create-checkout-session` Function

1. **Go to**: https://supabase.com/dashboard/project/olnhzuxguxjwcbsxytju/functions
2. **Click**: "Create a new function"
3. **Function name**: `create-checkout-session`
4. **Copy and paste this code**:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    const { items, customer_email, customer_name, shipping_address, metadata } = await req.json()

    const orderData = {
      user_id: metadata.user_id,
      customer_name,
      customer_email,
      customer_phone: metadata.phone || null,
      shipping_address,
      total_amount: items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0),
      status: 'pending',
      delivery_notes: metadata.delivery_notes || null,
    }

    const { data: order, error: orderError } = await supabaseClient
      .from('orders')
      .insert(orderData)
      .select()
      .single()

    if (orderError) {
      throw new Error(`Failed to create order: ${orderError.message}`)
    }

    const orderItems = items.map((item: any) => ({
      order_id: order.id,
      product_id: item.id,
      quantity: item.quantity,
      price_at_time: item.price,
      unit_price: item.price,
      total_price: item.price * item.quantity,
    }))

    const { error: itemsError } = await supabaseClient
      .from('order_items')
      .insert(orderItems)

    if (itemsError) {
      throw new Error(`Failed to create order items: ${itemsError.message}`)
    }

    const session = await stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      line_items: items.map((item: any) => ({
        price_data: {
          currency: 'usd',
          product_data: {
            name: item.name,
          },
          unit_amount: Math.round(item.price * 100),
        },
        quantity: item.quantity,
      })),
      mode: 'payment',
      return_url: `${req.headers.get('origin')}/payment/return?session_id={CHECKOUT_SESSION_ID}`,
      metadata: {
        order_id: order.id,
        user_id: metadata.user_id,
      },
      customer_email,
      shipping_address_collection: {
        allowed_countries: ['US', 'CA'],
      },
    })

    return new Response(
      JSON.stringify({
        client_secret: session.client_secret,
        session_id: session.id,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error creating checkout session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
```

5. **Click**: "Deploy function"

### Step 2: Create `retrieve-session` Function

1. **Click**: "Create a new function" again
2. **Function name**: `retrieve-session`
3. **Copy and paste this code**:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    const { session_id } = await req.json()

    const session = await stripe.checkout.sessions.retrieve(session_id)

    if (session.payment_status === 'paid' && session.metadata?.order_id) {
      const { error: updateError } = await supabaseClient
        .from('orders')
        .update({ 
          status: 'paid',
          stripe_session_id: session_id,
          updated_at: new Date().toISOString(),
        })
        .eq('id', session.metadata.order_id)

      if (updateError) {
        console.error('Error updating order status:', updateError)
      }
    }

    return new Response(
      JSON.stringify({
        payment_status: session.payment_status,
        customer_email: session.customer_email,
        metadata: session.metadata,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error retrieving session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
      },
    )
  }
})
```

4. **Click**: "Deploy function"

### Step 3: Test the Payment

1. **Refresh your application** at http://localhost:8080
2. **Try the payment flow** again
3. **Should work without edge function errors!**

## ✅ Verification

After deployment, you should see both functions listed in your Supabase Dashboard with "Active" status.

**This will fix the "failed to send a request to the edge function" error!** 🎉
