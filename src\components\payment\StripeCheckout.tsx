import React, { useState, useEffect } from 'react';
import { EmbeddedCheckoutProvider, EmbeddedCheckout } from '@stripe/react-stripe-js';
import { stripePromise, stripePublishableKey } from '@/lib/stripe';
import { paymentService, CreateCheckoutSessionData } from '@/services/paymentService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle } from 'lucide-react';

interface StripeCheckoutProps {
  checkoutData: CreateCheckoutSessionData;
  onSuccess?: (sessionId: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

export function StripeCheckout({ checkoutData, onSuccess, onError, onCancel }: StripeCheckoutProps) {
  const [clientSecret, setClientSecret] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  // Check if Stripe is configured
  if (!stripePublishableKey || !stripePromise) {
    return (
      <Card className="glass-effect">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-600">
            <AlertCircle className="h-5 w-5" />
            Stripe Not Configured
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Stripe payment processing is not configured yet. Please use the manual payment option below or contact support.
          </p>
          {onCancel && (
            <Button onClick={onCancel} variant="outline" className="w-full">
              Use Manual Payment
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  useEffect(() => {
    const createCheckoutSession = async () => {
      try {
        setLoading(true);
        setError('');
        
        const response = await paymentService.createCheckoutSession(checkoutData);
        setClientSecret(response.client_secret);
      } catch (err: any) {
        const errorMessage = err.message || 'Failed to initialize payment';
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    createCheckoutSession();
  }, [checkoutData, onError]);

  const options = {
    clientSecret,
    onComplete: () => {
      // This will be called when the payment is completed
      // The actual success handling will be done on the return page
      console.log('Payment completed');
    },
  };

  if (loading) {
    return (
      <Card className="glass-effect">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto" />
            <p className="text-muted-foreground">Initializing secure payment...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="glass-effect">
        <CardHeader>
          <CardTitle className="text-destructive">Payment Error</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">{error}</p>
          <div className="flex gap-2">
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
            {onCancel && (
              <Button onClick={onCancel} variant="ghost">
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card className="glass-effect">
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">Unable to initialize payment</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="stripe-checkout-container">
      <EmbeddedCheckoutProvider stripe={stripePromise} options={options}>
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </div>
  );
}
