import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Layout } from '@/components/layout/Layout';
import { paymentService } from '@/services/paymentService';
import { orderService } from '@/services/orderService';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/hooks/use-toast';

export default function PaymentReturn() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { clearCart } = useCart();
  const { toast } = useToast();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [orderId, setOrderId] = useState<string>('');

  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    
    if (!sessionId) {
      setStatus('error');
      setMessage('No payment session found');
      return;
    }

    const handlePaymentResult = async () => {
      try {
        // Retrieve the session from Stripe
        const session = await paymentService.retrieveSession(sessionId);
        
        if (session.payment_status === 'paid') {
          // Payment was successful
          setStatus('success');
          setMessage('Payment completed successfully!');
          
          // Update order status in database
          if (session.metadata?.order_id) {
            try {
              await orderService.updateOrderStatus(session.metadata.order_id, 'paid');
              setOrderId(session.metadata.order_id);
              
              // Clear the cart
              await clearCart();
              
              toast({
                title: "Payment Successful!",
                description: `Your order #${session.metadata.order_id.slice(0, 8).toUpperCase()} has been confirmed.`,
              });
            } catch (orderError) {
              console.error('Error updating order:', orderError);
              // Payment succeeded but order update failed
              toast({
                title: "Payment Successful",
                description: "Payment completed but there was an issue updating your order. Please contact support.",
                variant: "destructive",
              });
            }
          }
        } else {
          // Payment failed or was cancelled
          setStatus('error');
          setMessage('Payment was not completed. Please try again.');
        }
      } catch (error: any) {
        console.error('Error handling payment result:', error);
        setStatus('error');
        setMessage(error.message || 'An error occurred while processing your payment');
      }
    };

    handlePaymentResult();
  }, [searchParams, clearCart, toast]);

  const handleContinue = () => {
    if (status === 'success') {
      navigate('/orders');
    } else {
      navigate('/cart');
    }
  };

  return (
    <Layout>
      <div className="container max-w-md mx-auto py-16">
        <Card className="glass-effect">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {status === 'loading' && (
                <div className="rounded-full bg-blue-100 dark:bg-blue-900 p-3">
                  <Loader2 className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
                </div>
              )}
              {status === 'success' && (
                <div className="rounded-full bg-green-100 dark:bg-green-900 p-3">
                  <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              )}
              {status === 'error' && (
                <div className="rounded-full bg-red-100 dark:bg-red-900 p-3">
                  <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
                </div>
              )}
            </div>
            <CardTitle className="text-2xl gradient-text">
              {status === 'loading' && 'Processing Payment...'}
              {status === 'success' && 'Payment Successful!'}
              {status === 'error' && 'Payment Failed'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground">{message}</p>
            
            {orderId && (
              <div className="bg-muted p-4 rounded-lg">
                <p className="text-sm font-medium">Order ID</p>
                <p className="text-lg font-mono">#{orderId.slice(0, 8).toUpperCase()}</p>
              </div>
            )}
            
            {status !== 'loading' && (
              <div className="space-y-2">
                <Button onClick={handleContinue} className="w-full">
                  {status === 'success' ? 'View Orders' : 'Back to Cart'}
                </Button>
                
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/')}
                  className="w-full"
                >
                  Continue Shopping
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
