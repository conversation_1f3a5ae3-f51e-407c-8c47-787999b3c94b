
import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CreditCard, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { Layout } from '@/components/layout/Layout';
import { useToast } from '@/hooks/use-toast';
import { orderService } from '@/services/orderService';
import { CreateOrderData } from '@/types/order';
import { StripeCheckout } from '@/components/payment/StripeCheckout';
import { CreateCheckoutSessionData } from '@/services/paymentService';
import { stripePublishableKey } from '@/lib/stripe';

export default function Checkout() {
  const navigate = useNavigate();
  const { items, totalPrice, clearCart } = useCart();
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'stripe' | 'manual'>(
    stripePublishableKey ? 'stripe' : 'manual'
  );

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: user?.email || '',
    address: '',
    city: '',
    zipCode: '',
    state: '',
    country: 'United States',
    phone: '',
    deliveryNotes: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.firstName || !formData.lastName || !formData.address || !formData.city || !formData.zipCode) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const isFormValid = () => {
    return !!(formData.firstName && formData.lastName && formData.address && formData.city && formData.zipCode);
  };

  // Memoize the checkout data to prevent unnecessary re-renders of StripeCheckout
  const stripeCheckoutData = useMemo((): CreateCheckoutSessionData => {
    // Only create checkout data if form is valid
    if (!isFormValid()) {
      return {
        items: [],
        customer_email: '',
        customer_name: '',
        shipping_address: {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: '',
        },
        metadata: {},
      };
    }

    return {
      items: items.map(item => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
      })),
      customer_email: formData.email,
      customer_name: `${formData.firstName} ${formData.lastName}`,
      shipping_address: {
        street: formData.address,
        city: formData.city,
        state: formData.state,
        zip: formData.zipCode,
        country: formData.country,
      },
      metadata: {
        user_id: user?.id || '',
        phone: formData.phone,
        delivery_notes: formData.deliveryNotes,
      },
    };
  }, [
    formData.firstName,
    formData.lastName,
    formData.email,
    formData.address,
    formData.city,
    formData.state,
    formData.zipCode,
    formData.country,
    formData.phone,
    formData.deliveryNotes,
    items,
    user?.id,
  ]);

  const handlePayment = async () => {
    if (!user) {
      toast({
        title: "Please sign in",
        description: "You need to be signed in to complete your purchase",
        variant: "destructive",
      });
      // Redirect to login with current location so user can return after login
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }

    // Validate required fields
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      console.log('Checkout: Starting order creation process');

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create order data
      const orderData: CreateOrderData = {
        user_id: user.id,
        customer_name: `${formData.firstName} ${formData.lastName}`,
        customer_email: formData.email,
        customer_phone: formData.phone || undefined,
        shipping_address: {
          street: formData.address,
          city: formData.city,
          state: formData.state,
          zip: formData.zipCode,
          country: formData.country
        },
        total_amount: totalPrice,
        status: 'pending',
        delivery_notes: formData.deliveryNotes || undefined,
        order_items: items.map(item => ({
          product_id: item.id,
          quantity: item.quantity,
          price_at_time: item.price,
          unit_price: item.price,
          total_price: item.price * item.quantity
        }))
      };

      console.log('Checkout: Creating order with data:', orderData);

      // Create the order
      const { data: order, error } = await orderService.createOrder(orderData);

      if (error) {
        console.error('Checkout: Error creating order:', error);
        throw new Error(error.message || 'Failed to create order');
      }

      console.log('Checkout: Order created successfully:', order);

      // Clear the cart
      await clearCart();

      toast({
        title: "Payment successful!",
        description: `Your order #${order?.id.slice(0, 8).toUpperCase()} has been placed successfully.`,
      });

      navigate('/orders');
    } catch (error: any) {
      console.error('Checkout: Payment/order creation failed:', error);
      toast({
        title: "Payment failed",
        description: error.message || "There was an error processing your payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (items.length === 0) {
    return (
      <Layout>
        <div className="container py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Your cart is empty</h1>
            <Button onClick={() => navigate('/products')}>
              Browse Products
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8">
        <Button
          variant="ghost"
          onClick={() => navigate('/cart')}
          className="mb-6"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Cart
        </Button>

        <h1 className="text-3xl font-bold mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Billing Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      placeholder="John"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      placeholder="Doe"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    readOnly
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="+****************"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <Label htmlFor="address">Address *</Label>
                  <Input
                    id="address"
                    name="address"
                    placeholder="123 Main St"
                    value={formData.address}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      name="city"
                      placeholder="New York"
                      value={formData.city}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      name="state"
                      placeholder="NY"
                      value={formData.state}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="zipCode">ZIP Code *</Label>
                    <Input
                      id="zipCode"
                      name="zipCode"
                      placeholder="10001"
                      value={formData.zipCode}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="deliveryNotes">Delivery Notes (Optional)</Label>
                  <Input
                    id="deliveryNotes"
                    name="deliveryNotes"
                    placeholder="Special delivery instructions..."
                    value={formData.deliveryNotes}
                    onChange={handleInputChange}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={paymentMethod} onValueChange={(value) => setPaymentMethod(value as 'stripe' | 'manual')}>
                  <TabsList className={`grid w-full ${stripePublishableKey ? 'grid-cols-2' : 'grid-cols-1'}`}>
                    {stripePublishableKey && (
                      <TabsTrigger value="stripe">Stripe Checkout</TabsTrigger>
                    )}
                    <TabsTrigger value="manual">Manual Payment</TabsTrigger>
                  </TabsList>

                  {stripePublishableKey && (
                    <TabsContent value="stripe" className="space-y-4 mt-4">
                      <p className="text-sm text-muted-foreground">
                        Secure payment processing with Stripe. Supports all major credit cards.
                      </p>
                      {!isFormValid() ? (
                        <div className="p-4 border border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                          <p className="text-sm text-yellow-800 dark:text-yellow-200">
                            Please fill in all required billing information above to proceed with payment.
                          </p>
                        </div>
                      ) : (
                        <StripeCheckout
                          checkoutData={stripeCheckoutData}
                          onSuccess={(sessionId) => {
                            console.log('Payment successful:', sessionId);
                          }}
                          onError={(error) => {
                            toast({
                              title: "Payment Error",
                              description: error,
                              variant: "destructive",
                            });
                          }}
                          onCancel={() => {
                            setPaymentMethod('manual');
                          }}
                        />
                      )}
                    </TabsContent>
                  )}

                  <TabsContent value="manual" className="space-y-4 mt-4">
                    <div>
                      <Label htmlFor="cardNumber">Card Number</Label>
                      <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="expiry">Expiry Date</Label>
                        <Input id="expiry" placeholder="MM/YY" />
                      </div>
                      <div>
                        <Label htmlFor="cvv">CVV</Label>
                        <Input id="cvv" placeholder="123" />
                      </div>
                    </div>
                    <Button
                      onClick={handlePayment}
                      className="w-full hover-glow"
                      disabled={loading}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      {loading ? 'Processing...' : `Pay $${totalPrice.toFixed(2)}`}
                    </Button>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {items.map((item) => (
                  <div key={item.id} className="flex justify-between">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Qty: {item.quantity}
                      </p>
                    </div>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t pt-4">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span className="gradient-text">${totalPrice.toFixed(2)}</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground text-center">
                  Your payment information is secure and encrypted
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}
