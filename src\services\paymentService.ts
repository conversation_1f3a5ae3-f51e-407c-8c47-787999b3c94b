import { supabase } from '@/integrations/supabase/client';

export interface CreateCheckoutSessionData {
  items: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
  }>;
  customer_email: string;
  customer_name: string;
  shipping_address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  metadata?: Record<string, string>;
}

export interface CheckoutSessionResponse {
  client_secret: string;
  session_id: string;
}

class PaymentService {
  async createCheckoutSession(data: CreateCheckoutSessionData): Promise<CheckoutSessionResponse> {
    try {
      // First, try the edge function
      const { data: response, error } = await supabase.functions.invoke('create-checkout-session', {
        body: data,
      });

      if (error) {
        console.error('Edge function error:', error);

        // If edge function fails, provide a helpful error message
        if (error.message?.includes('Failed to send a request to the edge function') ||
            error.message?.includes('Function not found')) {
          throw new Error(
            'Payment processing is temporarily unavailable. The payment service needs to be deployed. ' +
            'Please contact support or try again later.'
          );
        }

        throw new Error(error.message || 'Failed to create checkout session');
      }

      return response;
    } catch (error: any) {
      console.error('Payment service error:', error);

      // Provide more specific error messages
      if (error.message?.includes('Failed to send a request to the edge function')) {
        throw new Error(
          'Payment service is not available. The Stripe integration requires deployment of edge functions. ' +
          'Please contact the administrator to deploy the payment service.'
        );
      }

      throw new Error(error.message || 'Payment service unavailable');
    }
  }

  async retrieveSession(sessionId: string) {
    try {
      const { data: response, error } = await supabase.functions.invoke('retrieve-session', {
        body: { session_id: sessionId },
      });

      if (error) {
        console.error('Error retrieving session:', error);
        throw new Error(error.message || 'Failed to retrieve session');
      }

      return response;
    } catch (error: any) {
      console.error('Payment service error:', error);
      throw new Error(error.message || 'Payment service unavailable');
    }
  }
}

export const paymentService = new PaymentService();
