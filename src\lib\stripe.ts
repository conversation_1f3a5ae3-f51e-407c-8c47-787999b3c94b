import { loadStripe } from '@stripe/stripe-js';

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;

// Only load Stripe if the key is available
const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

export { stripePromise, stripePublishableKey };
