import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    const { session_id } = await req.json()

    // Retrieve session from Stripe
    const session = await stripe.checkout.sessions.retrieve(session_id)

    // If payment was successful, update order status
    if (session.payment_status === 'paid' && session.metadata?.order_id) {
      const { error: updateError } = await supabaseClient
        .from('orders')
        .update({ 
          status: 'paid',
          stripe_session_id: session_id,
          updated_at: new Date().toISOString(),
        })
        .eq('id', session.metadata.order_id)

      if (updateError) {
        console.error('Error updating order status:', updateError)
      }
    }

    return new Response(
      JSON.stringify({
        payment_status: session.payment_status,
        customer_email: session.customer_email,
        metadata: session.metadata,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error retrieving session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
